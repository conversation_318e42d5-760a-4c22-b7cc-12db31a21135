import { useState, useEffect, useCallback } from 'react'
import { getDidYouMeanSuggestions, matchesSearch } from '@/lib/utils/search-utils'

interface SearchResult {
  id: string
  name: string
  image: string
  tag: string
  href: string
  type: 'champion' | 'skin' | 'chroma' | 'item'
}

interface SearchResponse {
  success: boolean
  data: SearchResult[]
  count: number
  query?: string
  type: 'random' | 'search' | 'empty'
  timestamp: string
}

// Cache for search data to avoid repeated API calls
let searchDataCache: SearchResult[] | null = null
let cacheTimestamp = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export function useSearch() {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [randomSuggestions, setRandomSuggestions] = useState<SearchResult[]>([])
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isSearching, setIsSearching] = useState(false) // Track if we're actively searching

  // Load all search data once and cache it
  const loadSearchData = useCallback(async (): Promise<SearchResult[]> => {
    // Check if we have valid cached data
    if (searchDataCache && (Date.now() - cacheTimestamp) < CACHE_DURATION) {
      return searchDataCache
    }

    try {
      const response = await fetch('/api/search?loadAll=true')
      const data: SearchResponse = await response.json()

      if (data.success) {
        searchDataCache = data.data
        cacheTimestamp = Date.now()
        return searchDataCache
      }
      return []
    } catch (error) {
      console.error('Failed to load search data:', error)
      return []
    }
  }, [])

  // Client-side search function with normalized matching
  const performClientSearch = useCallback((query: string, allData: SearchResult[]): SearchResult[] => {
    if (!query.trim()) return []

    const results: SearchResult[] = []

    // Search champions (limit 2) using normalized matching
    const championResults = allData
      .filter(item => item.type === 'champion' && matchesSearch(query, item.name))
      .slice(0, 2)
    results.push(...championResults)

    // Search skins (limit 2) using normalized matching
    const skinResults = allData
      .filter(item => item.type === 'skin' && matchesSearch(query, item.name))
      .slice(0, 2)
    results.push(...skinResults)

    // Search chromas (limit 2) using normalized matching
    const chromaResults = allData
      .filter(item => item.type === 'chroma' && matchesSearch(query, item.name))
      .slice(0, 2)
    results.push(...chromaResults)

    // Search items (limit 2) using normalized matching
    const itemResults = allData
      .filter(item => item.type === 'item' && matchesSearch(query, item.name))
      .slice(0, 2)
    results.push(...itemResults)

    return results.slice(0, 8) // Limit total to 8 results
  }, [])

  // Debounced search with client-side filtering
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([])
      setSearchSuggestions([])
      setIsSearching(false)
      setLoading(false)
      setError(null)
      return
    }

    // Set searching state immediately when user types
    setIsSearching(true)
    setError(null)

    const timeoutId = setTimeout(async () => {
      try {
        setLoading(true)

        // Load all search data (cached after first load)
        const allData = await loadSearchData()

        // Perform client-side search
        const results = performClientSearch(searchQuery, allData)

        setSearchResults(results)

        // Generate "Did You Mean?" suggestions if no results found
        if (results.length === 0) {
          const availableTerms = allData.map(item => item.name)
          const suggestions = getDidYouMeanSuggestions(searchQuery, availableTerms, 3, 0.4)
          setSearchSuggestions(suggestions)
        } else {
          setSearchSuggestions([])
        }

        setError(null)
      } catch (err) {
        console.error('Search error:', err)
        setError('Search failed')
        setSearchResults([])
      } finally {
        setLoading(false)
        setIsSearching(false)
      }
    }, 150) // Reduced debounce delay for faster UX

    return () => {
      clearTimeout(timeoutId)
      // Don't reset loading here as it causes flickering
    }
  }, [searchQuery, loadSearchData, performClientSearch])

  // Generate random suggestions from cached data
  const generateRandomSuggestions = useCallback((allData: SearchResult[]): SearchResult[] => {
    const champions = allData.filter(item => item.type === 'champion')
    const skins = allData.filter(item => item.type === 'skin')
    const chromas = allData.filter(item => item.type === 'chroma')
    const items = allData.filter(item => item.type === 'item')

    const suggestions: SearchResult[] = []

    // Get 2 random champions
    if (champions.length > 0) {
      const shuffled = [...champions].sort(() => 0.5 - Math.random())
      suggestions.push(...shuffled.slice(0, 2))
    }

    // Get 2 random skins
    if (skins.length > 0) {
      const shuffled = [...skins].sort(() => 0.5 - Math.random())
      suggestions.push(...shuffled.slice(0, 2))
    }

    // Get 1 random chroma
    if (chromas.length > 0) {
      const randomChroma = chromas[Math.floor(Math.random() * chromas.length)]
      suggestions.push(randomChroma)
    }

    // Get 1 random item
    if (items.length > 0) {
      const randomItem = items[Math.floor(Math.random() * items.length)]
      suggestions.push(randomItem)
    }

    return suggestions
  }, [])

  // Fetch random suggestions
  const fetchRandomSuggestions = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Try to use cached data first
      const allData = await loadSearchData()
      if (allData.length > 0) {
        const suggestions = generateRandomSuggestions(allData)
        setRandomSuggestions(suggestions)
      } else {
        // Fallback to API if no cached data
        const response = await fetch('/api/search?random=true')
        const data: SearchResponse = await response.json()

        if (data.success) {
          setRandomSuggestions(data.data)
        } else {
          setError('Failed to fetch suggestions')
          setRandomSuggestions([])
        }
      }
    } catch (err) {
      console.error('Random suggestions error:', err)
      setError('Failed to fetch suggestions')
      setRandomSuggestions([])
    } finally {
      setLoading(false)
    }
  }, [loadSearchData, generateRandomSuggestions])

  // Get current suggestions (search results or random suggestions)
  const getCurrentSuggestions = useCallback(() => {
    if (searchQuery.trim()) {
      return searchResults
    }
    return randomSuggestions
  }, [searchQuery, searchResults, randomSuggestions])

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery('')
    setSearchResults([])
    setSearchSuggestions([])
    setError(null)
    setLoading(false)
    setIsSearching(false)
  }, [])

  // Handle "Did You Mean?" suggestion clicks
  const handleSuggestionClick = useCallback((suggestion: string) => {
    setSearchQuery(suggestion)
  }, [])

  return {
    searchQuery,
    setSearchQuery,
    searchResults,
    randomSuggestions,
    searchSuggestions,
    currentSuggestions: getCurrentSuggestions(),
    loading,
    isSearching,
    error,
    fetchRandomSuggestions,
    clearSearch,
    handleSuggestionClick,
    hasQuery: searchQuery.trim().length > 0,
    hasSearchResults: searchResults.length > 0,
    showSuggestions: searchSuggestions.length > 0 && searchResults.length === 0 && searchQuery.trim().length > 0
  }
}

// Hook for mobile search with navigation
export function useMobileSearch() {
  const [isOpen, setIsOpen] = useState(false)
  const search = useSearch()

  const openSearch = useCallback(() => {
    setIsOpen(true)
    // Fetch random suggestions when opening
    if (search.randomSuggestions.length === 0) {
      search.fetchRandomSuggestions()
    }
  }, [search])

  const closeSearch = useCallback(() => {
    setIsOpen(false)
    search.clearSearch()
  }, [search])

  const handleResultClick = useCallback((href: string) => {
    window.location.href = href
    closeSearch()
  }, [closeSearch])

  return {
    ...search,
    isOpen,
    openSearch,
    closeSearch,
    handleResultClick
  }
}
